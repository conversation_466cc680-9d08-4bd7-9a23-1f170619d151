const AutoRegister = require('./index.js');
const AugmentAuth = require('./augment-auth.js');
const TokenStorage = require('./token-storage.js');
const AntiFingerprint = require('./anti-fingerprint-config.js');
require('dotenv').config();

/**
 * 增强版邮箱验证流程 - 专门对抗指纹检测
 */
async function runEnhancedEmailVerification() {
    const autoRegister = new AutoRegister();
    const augmentAuth = new AugmentAuth();
    const tokenStorage = new TokenStorage();

    try {
        console.log('🛡️  启动增强版 Augment 授权和邮箱验证流程');
        console.log('🔒 使用强化抗指纹检测技术');
        console.log('');

        // 步骤1: 生成 Augment 授权 URL
        console.log('🔐 步骤1: 生成 Augment 授权 URL');
        const authUrl = augmentAuth.generateAuthUrl();

        // 将生成的 URL 设置为环境变量
        process.env.LINK_TO_TEST = authUrl;

        console.log('✅ 授权 URL 已生成并设置');
        console.log(`📧 目标URL: ${authUrl}`);
        console.log('🛡️  启用增强抗指纹检测模式');
        console.log('🎭 模拟真实用户行为模式');
        console.log('⏰ 使用随机化时间延迟');
        console.log('🖱️  模拟自然鼠标移动和滚动');
        console.log('📝 使用One Mail API生成临时邮箱');
        console.log('⏰ 将自动获取验证码（3分钟超时，每8秒检查一次）');
        console.log('📸 每一步都会自动截图和保存HTML');
        console.log('');

        // 添加随机启动延迟，模拟真实用户行为
        const startupDelay = AntiFingerprint.randomBetween(2000, 5000);
        console.log(`⏳ 启动延迟 ${Math.round(startupDelay/1000)} 秒，模拟真实用户行为...`);
        await new Promise(resolve => setTimeout(resolve, startupDelay));

        // 步骤2: 执行增强版邮箱验证流程
        console.log('🔐 步骤2: 执行增强版邮箱验证和授权流程');
        console.log('🎯 使用非headless模式，更难被检测');
        console.log('🔄 应用多层抗指纹技术');
        
        const clipboardContent = await autoRegister.handleEmailVerificationWithOneMailAPI(authUrl);

        // 步骤3: 处理真实授权码并获取真实访问令牌
        if (clipboardContent) {
            console.log('');
            console.log('🔐 步骤3: 处理真实授权码并获取真实访问令牌');
            console.log('📋 从剪贴板获取到的真实授权码:');
            console.log(clipboardContent);
            console.log('');

            try {
                // 完成真实的 OAuth 流程
                console.log('🚀 开始调用真实的 Augment API...');
                const tokenResponse = await augmentAuth.completeOAuthFlow(clipboardContent);

                console.log('');
                console.log('✅ 真实 API 调用成功！获取到真实访问令牌！');
                console.log(`🔑 真实访问令牌: ${tokenResponse.access_token?.substring(0, 30)}...`);
                console.log(`🏢 租户 URL: ${tokenResponse.tenant_url}`);

                // 保存真实令牌到 JSON 文件
                const tokenId = tokenStorage.addToken(tokenResponse, {
                    description: 'Real token from Enhanced Anti-Fingerprint Verification',
                    user_agent: 'augment-auto-enhanced-verification',
                    session_id: `enhanced_session_${Date.now()}`,
                    anti_fingerprint_version: '2.0'
                });

                console.log('');
                console.log('🎉 增强版验证流程成功完成！');
                console.log(`💾 真实令牌已保存到 tokens.json，ID: ${tokenId}`);
                console.log('🛡️  成功绕过指纹检测系统！');
                console.log('📊 令牌统计:');
                tokenStorage.getStats();

            } catch (tokenError) {
                console.error('');
                console.error('❌ 真实 API 调用失败:', tokenError.message);
                console.error('📋 剪贴板内容已保存，可手动处理');
                console.error('🔍 错误详情:', tokenError.stack);
                
                // 如果是指纹检测相关的错误，提供建议
                if (tokenError.message.includes('rejected') || tokenError.message.includes('blocked')) {
                    console.error('');
                    console.error('🛡️  可能仍被指纹检测系统识别，建议：');
                    console.error('1. 等待更长时间后重试');
                    console.error('2. 使用不同的网络环境');
                    console.error('3. 清理浏览器缓存和数据');
                }
            }
        }

        console.log('');
        console.log('🎉 增强版邮箱验证流程完成！');
        console.log('📁 请查看 image/ 目录中的截图和HTML文件');
        console.log('📁 请查看 tokens.json 文件中的令牌数据');
        console.log('🛡️  抗指纹检测技术已应用');

    } catch (error) {
        console.error('');
        console.error('💥 增强版邮箱验证流程失败:', error.message);
        console.error('📁 请查看 image/ 目录中的错误截图和HTML文件进行调试');
        
        // 提供针对性的错误建议
        if (error.message.includes('Sign-up rejected')) {
            console.error('');
            console.error('🛡️  检测到"Sign-up rejected"错误，这表明仍被反欺诈系统识别');
            console.error('💡 建议解决方案：');
            console.error('1. 增加更多随机延迟时间');
            console.error('2. 使用真实的用户数据目录');
            console.error('3. 考虑使用代理IP');
            console.error('4. 等待更长时间后重试（24小时）');
        }
        
        process.exit(1);
    }
}

if (require.main === module) {
    runEnhancedEmailVerification();
}

module.exports = runEnhancedEmailVerification;
