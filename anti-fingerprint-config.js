/**
 * 抗指纹检测配置模块
 * 用于增强Puppeteer浏览器的隐蔽性，避免被检测为自动化工具
 */

const crypto = require('crypto');

/**
 * 生成随机数值
 */
function randomBetween(min, max) {
    return Math.floor(Math.random() * (max - min + 1)) + min;
}

/**
 * 生成随机字符串
 */
function randomString(length) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
}

/**
 * 获取随机User-Agent
 */
function getRandomUserAgent() {
    const userAgents = [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36'
    ];
    return userAgents[Math.floor(Math.random() * userAgents.length)];
}

/**
 * 获取增强的浏览器启动参数
 */
function getEnhancedLaunchOptions() {
    const width = randomBetween(1200, 1920);
    const height = randomBetween(800, 1080);

    return {
        headless: false, // 改为非headless模式，更难检测
        args: [
            // 基础安全参数
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-dev-shm-usage',
            '--disable-web-security',
            '--disable-features=VizDisplayCompositor',

            // 强化抗检测参数
            '--disable-blink-features=AutomationControlled',
            '--exclude-switches=enable-automation',
            '--disable-extensions-http-throttling',
            '--disable-ipc-flooding-protection',
            '--disable-renderer-backgrounding',
            '--disable-backgrounding-occluded-windows',
            '--disable-background-timer-throttling',
            '--disable-features=TranslateUI,BlinkGenPropertyTrees',
            '--disable-default-apps',
            '--disable-sync',
            '--disable-translate',
            '--hide-scrollbars',
            '--mute-audio',
            '--no-first-run',
            '--no-default-browser-check',
            '--no-pings',
            '--password-store=basic',
            '--use-mock-keychain',
            '--disable-component-update',
            '--disable-domain-reliability',
            '--disable-features=AudioServiceOutOfProcess',
            '--disable-print-preview',
            '--disable-speech-api',
            '--disable-file-system',
            '--disable-presentation-api',
            '--disable-permissions-api',
            '--disable-new-zip-unpacker',
            '--disable-media-session-api',

            // WebRTC 相关
            '--disable-webrtc-multiple-routes',
            '--disable-webrtc-hw-decoding',
            '--disable-webrtc-hw-encoding',
            '--enforce-webrtc-ip-permission-check',

            // 内存和性能优化
            '--memory-pressure-off',
            '--max_old_space_size=4096',
            '--js-flags=--max-old-space-size=4096',

            // 网络相关
            '--aggressive-cache-discard',
            '--disable-background-networking',
            '--disable-client-side-phishing-detection',
            '--disable-component-extensions-with-background-pages',
            '--disable-hang-monitor',
            '--disable-prompt-on-repost',
            '--disable-background-mode',
            '--disable-add-to-shelf',
            '--disable-datasaver-prompt',

            // 窗口设置
            `--window-size=${width},${height}`,
            `--window-position=${randomBetween(0, 100)},${randomBetween(0, 100)}`,

            // 语言和地区设置
            '--lang=en-US,en',
            '--accept-lang=en-US,en;q=0.9,zh-CN;q=0.8',

            // 用户数据目录（使用临时目录）
            `--user-data-dir=/tmp/chrome_dev_test_${Date.now()}_${randomBetween(1000, 9999)}`
        ],
        defaultViewport: {
            width: width,
            height: height,
            deviceScaleFactor: 1,
            isMobile: false,
            hasTouch: false,
            isLandscape: true
        },
        timeout: 120000,
        ignoreDefaultArgs: [
            '--enable-automation',
            '--enable-blink-features=IdleDetection',
            '--enable-logging',
            '--disable-extensions',
            '--disable-default-apps',
            '--disable-component-extensions-with-background-pages'
        ],
        ignoreHTTPSErrors: true,
        slowMo: randomBetween(10, 50) // 添加随机慢动作
    };
}

/**
 * 应用抗指纹检测脚本到页面
 */
async function applyAntiFingerprinting(page) {
    // 设置随机User-Agent
    await page.setUserAgent(getRandomUserAgent());

    // 设置额外的HTTP头
    await page.setExtraHTTPHeaders({
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Encoding': 'gzip, deflate, br',
        'Accept-Language': 'en-US,en;q=0.9',
        'Cache-Control': 'max-age=0',
        'Sec-Ch-Ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
        'Sec-Ch-Ua-Mobile': '?0',
        'Sec-Ch-Ua-Platform': '"Windows"',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Sec-Fetch-User': '?1',
        'Upgrade-Insecure-Requests': '1'
    });

    // 在每个新文档加载时执行抗指纹脚本
    await page.evaluateOnNewDocument(() => {
        // 完全移除webdriver相关属性
        Object.defineProperty(navigator, 'webdriver', {
            get: () => undefined,
            configurable: true
        });

        // 移除自动化检测标识
        delete window.cdc_adoQpoasnfa76pfcZLmcfl_Array;
        delete window.cdc_adoQpoasnfa76pfcZLmcfl_Promise;
        delete window.cdc_adoQpoasnfa76pfcZLmcfl_Symbol;
        delete window.cdc_adoQpoasnfa76pfcZLmcfl_JSON;
        delete window.cdc_adoQpoasnfa76pfcZLmcfl_Object;
        delete window.cdc_adoQpoasnfa76pfcZLmcfl_Proxy;
        delete window.cdc_adoQpoasnfa76pfcZLmcfl_Reflect;

        // 伪装chrome对象
        window.chrome = {
            app: {
                isInstalled: false,
                InstallState: {
                    DISABLED: 'disabled',
                    INSTALLED: 'installed',
                    NOT_INSTALLED: 'not_installed'
                },
                RunningState: {
                    CANNOT_RUN: 'cannot_run',
                    READY_TO_RUN: 'ready_to_run',
                    RUNNING: 'running'
                }
            },
            runtime: {
                onConnect: undefined,
                onMessage: undefined,
                onStartup: undefined,
                onInstalled: undefined,
                onSuspend: undefined,
                onSuspendCanceled: undefined,
                connect: function () { },
                sendMessage: function () { },
                getManifest: function () {
                    return {
                        name: 'Chrome',
                        version: '120.0.6099.109'
                    };
                }
            },
            loadTimes: function () {
                return {
                    commitLoadTime: Date.now() / 1000 - Math.random() * 100,
                    connectionInfo: 'http/1.1',
                    finishDocumentLoadTime: Date.now() / 1000 - Math.random() * 10,
                    finishLoadTime: Date.now() / 1000 - Math.random() * 10,
                    firstPaintAfterLoadTime: 0,
                    firstPaintTime: Date.now() / 1000 - Math.random() * 10,
                    navigationType: 'Other',
                    npnNegotiatedProtocol: 'unknown',
                    requestTime: Date.now() / 1000 - Math.random() * 100,
                    startLoadTime: Date.now() / 1000 - Math.random() * 100,
                    wasAlternateProtocolAvailable: false,
                    wasFetchedViaSpdy: false,
                    wasNpnNegotiated: false
                };
            },
            csi: function () {
                return {
                    pageT: Date.now() - Math.random() * 1000,
                    startE: Date.now() - Math.random() * 1000,
                    tran: Math.floor(Math.random() * 20)
                };
            }
        };

        // 伪装语言设置
        Object.defineProperty(navigator, 'languages', {
            get: () => ['en-US', 'en'],
            configurable: true
        });

        // 伪装插件
        Object.defineProperty(navigator, 'plugins', {
            get: () => [
                {
                    0: { type: "application/x-google-chrome-pdf", suffixes: "pdf", description: "Portable Document Format" },
                    description: "Portable Document Format",
                    filename: "internal-pdf-viewer",
                    length: 1,
                    name: "Chrome PDF Plugin"
                },
                {
                    0: { type: "application/pdf", suffixes: "pdf", description: "" },
                    description: "",
                    filename: "mhjfbmdgcfjbbpaeojofohoefgiehjai",
                    length: 1,
                    name: "Chrome PDF Viewer"
                }
            ],
            configurable: true
        });

        // 伪装权限API
        const originalQuery = window.navigator.permissions.query;
        window.navigator.permissions.query = (parameters) => (
            parameters.name === 'notifications' ?
                Promise.resolve({ state: Notification.permission }) :
                originalQuery(parameters)
        );

        // 伪装硬件并发数
        Object.defineProperty(navigator, 'hardwareConcurrency', {
            get: () => Math.floor(Math.random() * 8) + 4,
            configurable: true
        });

        // 伪装设备内存
        Object.defineProperty(navigator, 'deviceMemory', {
            get: () => [2, 4, 8][Math.floor(Math.random() * 3)],
            configurable: true
        });

        // 伪装平台
        Object.defineProperty(navigator, 'platform', {
            get: () => 'Win32',
            configurable: true
        });

        // 伪装 WebRTC
        const originalRTCPeerConnection = window.RTCPeerConnection;
        if (originalRTCPeerConnection) {
            window.RTCPeerConnection = function (...args) {
                const pc = new originalRTCPeerConnection(...args);
                const originalCreateDataChannel = pc.createDataChannel;
                pc.createDataChannel = function () {
                    const channel = originalCreateDataChannel.apply(this, arguments);
                    Object.defineProperty(channel, 'label', {
                        get: () => Math.random().toString(36).substring(7)
                    });
                    return channel;
                };
                return pc;
            };
        }

        // 伪装 AudioContext
        const originalAudioContext = window.AudioContext || window.webkitAudioContext;
        if (originalAudioContext) {
            const AudioContextProxy = function () {
                const context = new originalAudioContext();
                const originalCreateOscillator = context.createOscillator;
                context.createOscillator = function () {
                    const oscillator = originalCreateOscillator.call(this);
                    const originalStart = oscillator.start;
                    oscillator.start = function () {
                        // 添加微小的随机延迟
                        setTimeout(() => originalStart.call(this), Math.random() * 10);
                    };
                    return oscillator;
                };
                return context;
            };
            window.AudioContext = AudioContextProxy;
            if (window.webkitAudioContext) {
                window.webkitAudioContext = AudioContextProxy;
            }
        }

        // 移除自动化相关属性
        delete window.cdc_adoQpoasnfa76pfcZLmcfl_Array;
        delete window.cdc_adoQpoasnfa76pfcZLmcfl_Promise;
        delete window.cdc_adoQpoasnfa76pfcZLmcfl_Symbol;

        // 伪装WebGL指纹
        const getParameter = WebGLRenderingContext.prototype.getParameter;
        WebGLRenderingContext.prototype.getParameter = function (parameter) {
            if (parameter === 37445) {
                return 'Intel Inc.';
            }
            if (parameter === 37446) {
                return 'Intel(R) Iris(TM) Graphics 6100';
            }
            return getParameter(parameter);
        };

        // 伪装Canvas指纹
        const originalToDataURL = HTMLCanvasElement.prototype.toDataURL;
        HTMLCanvasElement.prototype.toDataURL = function () {
            const context = this.getContext('2d');
            if (context) {
                context.fillStyle = `rgba(${Math.floor(Math.random() * 255)}, ${Math.floor(Math.random() * 255)}, ${Math.floor(Math.random() * 255)}, 0.01)`;
                context.fillRect(0, 0, 1, 1);
            }
            return originalToDataURL.apply(this, arguments);
        };

        // 伪装屏幕分辨率
        Object.defineProperty(screen, 'width', {
            get: () => 1920 + Math.floor(Math.random() * 100),
            configurable: true
        });
        Object.defineProperty(screen, 'height', {
            get: () => 1080 + Math.floor(Math.random() * 100),
            configurable: true
        });

        // 伪装时区
        Date.prototype.getTimezoneOffset = function () {
            return -480; // UTC+8
        };

        // 添加真实的鼠标和键盘事件监听器
        ['mousedown', 'mouseup', 'mousemove', 'keydown', 'keyup'].forEach(eventType => {
            document.addEventListener(eventType, () => { }, true);
        });
    });
}

/**
 * 模拟人类行为的延迟
 */
function humanDelay() {
    return randomBetween(100, 300);
}

/**
 * 模拟页面加载后的自然等待
 */
function pageLoadDelay() {
    return randomBetween(1000, 3000);
}

/**
 * 模拟用户阅读和思考的时间
 */
function thinkingDelay() {
    return randomBetween(500, 2000);
}

/**
 * 模拟人类打字行为
 */
async function humanType(page, selector, text) {
    const element = await page.$(selector);
    if (!element) return false;

    await element.click();
    await page.waitForTimeout(humanDelay());

    for (const char of text) {
        await element.type(char, { delay: randomBetween(50, 150) });
        if (Math.random() < 0.1) { // 10%的概率暂停
            await page.waitForTimeout(randomBetween(200, 500));
        }
    }
    return true;
}

/**
 * 模拟人类鼠标移动和点击
 */
async function humanClick(page, selector) {
    const element = await page.$(selector);
    if (!element) return false;

    const box = await element.boundingBox();
    if (!box) return false;

    // 随机点击位置
    const x = box.x + box.width * (0.3 + Math.random() * 0.4);
    const y = box.y + box.height * (0.3 + Math.random() * 0.4);

    // 模拟鼠标移动
    await page.mouse.move(x, y, { steps: randomBetween(5, 15) });
    await page.waitForTimeout(randomBetween(100, 300));

    // 点击
    await page.mouse.click(x, y, { delay: randomBetween(50, 150) });
    return true;
}

/**
 * 模拟自然的页面滚动
 */
async function naturalScroll(page) {
    const scrollSteps = randomBetween(2, 5);
    for (let i = 0; i < scrollSteps; i++) {
        const scrollY = randomBetween(100, 300);
        await page.evaluate((y) => {
            window.scrollBy(0, y);
        }, scrollY);
        await page.waitForTimeout(randomBetween(200, 800));
    }
}

/**
 * 模拟随机鼠标移动
 */
async function randomMouseMovement(page) {
    const viewport = page.viewport();
    const movements = randomBetween(3, 8);

    for (let i = 0; i < movements; i++) {
        const x = randomBetween(0, viewport.width);
        const y = randomBetween(0, viewport.height);
        await page.mouse.move(x, y, { steps: randomBetween(5, 15) });
        await page.waitForTimeout(randomBetween(100, 500));
    }
}

/**
 * 模拟页面加载后的自然行为
 */
async function simulateNaturalBehavior(page) {
    // 等待页面完全加载
    await page.waitForTimeout(pageLoadDelay());

    // 随机滚动
    if (Math.random() < 0.7) {
        await naturalScroll(page);
    }

    // 随机鼠标移动
    if (Math.random() < 0.8) {
        await randomMouseMovement(page);
    }

    // 思考时间
    await page.waitForTimeout(thinkingDelay());
}

module.exports = {
    getEnhancedLaunchOptions,
    applyAntiFingerprinting,
    humanDelay,
    pageLoadDelay,
    thinkingDelay,
    humanType,
    humanClick,
    naturalScroll,
    randomMouseMovement,
    simulateNaturalBehavior,
    randomBetween,
    randomString,
    getRandomUserAgent
};
