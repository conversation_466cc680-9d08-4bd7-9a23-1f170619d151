{"name": "shell", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "test-onemail": "node test-onemail.js", "email-verification": "node run-email-verification.js", "enhanced-verification": "node run-enhanced-verification.js", "start": "node index.js", "token-api": "node token-api.js", "start-token-api": "node start-token-api.js", "test-token-api": "node test-api.js"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"axios": "^1.10.0", "clipboardy": "^4.0.0", "cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^5.1.0", "imapflow": "^1.0.191", "mailparser": "^3.7.4", "nodemailer": "^7.0.5", "puppeteer": "^24.12.1", "puppeteer-extra": "^3.3.6", "puppeteer-extra-plugin-stealth": "^2.11.2", "uuid": "^11.1.0"}}