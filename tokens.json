[{"id": "9d4cada7-7dfe-4c4d-83a1-b8d35bf5cbdf", "createdTime": "2025-08-15T11:15:05.583Z", "createdTimestamp": 1755256505583, "access_token": "c7dfeff57b2149bac7fd4c6d5c98b948136af6210201dc73e31909e5f740c534", "tenant_url": "https://d19.api.augmentcode.com/", "oauth_state": {"codeVerifier": "KleaoUWHyjUYiEmPRwGyXecSN6YtRRxqd4r__vg0_Cg", "codeChallenge": "HjjJ7XsJ7HLBqvx5hZYAR7zUJnAVzXqVTuuBGZ4jjzo", "state": "NidS2fDiAxs", "creationTime": 1755256443502}, "parsed_code": {"code": "_9c390bcce94e61aa1bf58c04510db2be", "state": "NidS2fDiAxs", "tenant_url": "https://d19.api.augmentcode.com/"}, "portal_url": null, "email_note": null, "description": "Real token from Augment API via email verification", "metadata": {"user_agent": "augment-auto-email-verification", "ip_address": null, "session_id": "session_1755256505582"}, "used": true}, {"id": "6d341ed7-3966-4a17-9c85-bf32d1e9ba5e", "createdTime": "2025-08-15T11:24:56.047Z", "createdTimestamp": 1755257096047, "access_token": "28441800454b57fa3d2ae0dee4a1f61d1780495552fa6840d6243a6281d13ad1", "tenant_url": "https://d19.api.augmentcode.com/", "oauth_state": {"codeVerifier": "vOi2ulDjpe_CgV0TYfPMYeDhRmdfBKmneqFxqN2h7hE", "codeChallenge": "Vo1CFHWNzp4LhsO8asw4dNGeNh7U2JzWSjik_b53SCg", "state": "sQ-8aLnViII", "creationTime": 1755257034775}, "parsed_code": {"code": "_fd6999ea9289634ea6fb642423094b0d", "state": "sQ-8aLnViII", "tenant_url": "https://d19.api.augmentcode.com/"}, "portal_url": null, "email_note": null, "description": "Real token from Augment API via email verification", "metadata": {"user_agent": "augment-auto-email-verification", "ip_address": null, "session_id": "session_1755257096046"}, "used": true}]